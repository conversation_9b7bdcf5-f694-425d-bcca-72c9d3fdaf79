'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Play, X } from 'lucide-react';
import { Dialog, DialogContent, DialogClose } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getFeaturedVideos, getYouTubeThumbnail, type Video } from '@/lib/api';
import { useStaggeredScrollAnimation } from '@/hooks/useScrollAnimation';

export default function FeaturedWork() {
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const containerRef = useStaggeredScrollAnimation();

  useEffect(() => {
    const fetchVideos = async () => {
      try {
        const featuredVideos = await getFeaturedVideos();
        // Show only first 6 videos for featured section
        setVideos(featuredVideos.slice(0, 6));
      } catch (error) {
        console.error('Error fetching featured videos:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, []);

  const getYouTubeEmbedUrl = (videoId: string) => {
    return `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`;
  };

  return (
    <section id="featured-work" className="py-20 bg-background" ref={containerRef}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-primary mb-4">
            Featured Work
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            A selection of projects showcasing diverse editing styles and creative storytelling techniques.
          </p>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-card rounded-xl overflow-hidden shadow-lg">
                <div className="aspect-video bg-gray-200 animate-pulse"></div>
                <div className="p-6">
                  <div className="h-6 bg-gray-200 rounded animate-pulse mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : videos.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground">No featured videos available at the moment.</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {videos.map((video, index) => (
                <div
                  key={video._id}
                                                                                                                                                                                                                                                                                                                                          className={`group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 h-[380px] flex flex-col animate-on-scroll stagger-${Math.min(index + 1, 6)}`}
                >
                  <div className="relative aspect-video overflow-hidden flex-shrink-0">
                    <Image
                      src={getYouTubeThumbnail(video.id)}
                      alt={video.title}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />

                    {/* Play Button Overlay */}
                    <Button
                      variant="ghost"
                      className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 h-full w-full rounded-none hover:bg-black/40 hover:cursor-pointer"
                      onClick={() => setSelectedVideo(video.id)}
                    >
                      <div className="w-16 h-16 bg-secondary hover:bg-secondary/90 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg">
                        <Play size={24} className="text-secondary-foreground ml-1" fill="currentColor" />
                      </div>
                    </Button>

                    {/* Category Badge */}
                    {video.category && (
                      <div className="absolute top-4 left-4">
                        <Badge variant="secondary" className="bg-accent/90 text-accent-foreground">
                          {video.category}
                        </Badge>
                      </div>
                    )}
                  </div>

                  <div className="p-6 flex-1 flex flex-col">
                    <h3 className="text-xl font-heading font-semibold text-primary mb-2 group-hover:text-secondary transition-colors duration-300 line-clamp-2">
                      {video.title}
                    </h3>
                    {video.description && (
                      <p className="text-muted-foreground text-sm leading-relaxed line-clamp-3 flex-1">
                        {video.description}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Single Dialog for all videos */}
            <Dialog
              open={selectedVideo !== null}
              onOpenChange={(open) => {
                if (!open) {
                  setSelectedVideo(null);
                }
              }}
            >
              <DialogContent className="w-[95vw] sm:max-w-2xl md:max-w-4xl lg:max-w-6xl p-0 bg-black border-0">
                <div className="relative w-full aspect-video">
                  <DialogClose asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors h-auto w-auto p-2"
                    >
                      <X size={24} />
                    </Button>
                  </DialogClose>
                  {selectedVideo && (
                    <iframe
                      src={getYouTubeEmbedUrl(selectedVideo)}
                      title={videos.find(v => v.id === selectedVideo)?.title || 'Video'}
                      className="w-full h-full"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </>
        )}

        {/* View More Button */}
        <div className="text-center mt-12">
          <Button asChild className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg">
            <Link href="/videos">
              View All Videos
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
