{"/api/auth/login/route": "app/api/auth/login/route.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/api/blog/slug/[slug]/route": "app/api/blog/slug/[slug]/route.js", "/api/cors-test/route": "app/api/cors-test/route.js", "/api/init/route": "app/api/init/route.js", "/api/public/blog/route": "app/api/public/blog/route.js", "/api/public/blog/[slug]/route": "app/api/public/blog/[slug]/route.js", "/api/status/route": "app/api/status/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/clients/route": "app/api/clients/route.js", "/api/blog/route": "app/api/blog/route.js", "/api/blog/[id]/route": "app/api/blog/[id]/route.js", "/api/clients/[id]/route": "app/api/clients/[id]/route.js", "/api/reels/route": "app/api/reels/route.js", "/api/reels/migrate/route": "app/api/reels/migrate/route.js", "/api/testimonials/route": "app/api/testimonials/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/videos/route": "app/api/videos/route.js", "/api/testimonials/[id]/route": "app/api/testimonials/[id]/route.js", "/api/reels/[id]/route": "app/api/reels/[id]/route.js", "/api/videos/[id]/route": "app/api/videos/[id]/route.js", "/dashboard/blog/new/page": "app/dashboard/blog/new/page.js", "/dashboard/clients/[id]/page": "app/dashboard/clients/[id]/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/clients/new/page": "app/dashboard/clients/new/page.js", "/dashboard/clients/page": "app/dashboard/clients/page.js", "/dashboard/reels/[id]/page": "app/dashboard/reels/[id]/page.js", "/dashboard/blog/[id]/page": "app/dashboard/blog/[id]/page.js", "/dashboard/reels/new/page": "app/dashboard/reels/new/page.js", "/dashboard/blog/page": "app/dashboard/blog/page.js", "/dashboard/testimonials/new/page": "app/dashboard/testimonials/new/page.js", "/dashboard/reels/page": "app/dashboard/reels/page.js", "/dashboard/videos/[id]/page": "app/dashboard/videos/[id]/page.js", "/dashboard/testimonials/[id]/page": "app/dashboard/testimonials/[id]/page.js", "/dashboard/videos/page": "app/dashboard/videos/page.js", "/dashboard/testimonials/page": "app/dashboard/testimonials/page.js", "/login/page": "app/login/page.js", "/dashboard/videos/new/page": "app/dashboard/videos/new/page.js", "/page": "app/page.js", "/setup/page": "app/setup/page.js"}