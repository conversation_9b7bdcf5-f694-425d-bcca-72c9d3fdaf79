{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "MWbr_334LAnSoH9iOVF2d", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "z2WXuwZtdGk/LJ62+5rY6tUPjYA9705wjsFO5fRpOeU=", "__NEXT_PREVIEW_MODE_ID": "15d1425eeae4c28508c447bc31e189fb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "acba4ee50f46c7799edbcc3309a7f907cf08942cd5884c353469cef481ae9a57", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1e2607ccb97abf28d69985dd04b58ff22b19826f803a2b098393a97974a06b79"}}}, "functions": {}, "sortedMiddleware": ["/"]}