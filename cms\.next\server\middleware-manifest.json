{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Rt9dgtkIVMGkSqpvCydJl", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "z2WXuwZtdGk/LJ62+5rY6tUPjYA9705wjsFO5fRpOeU=", "__NEXT_PREVIEW_MODE_ID": "85182a6972c3311004d7ec0f92e244b3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d45fb1757e7da810900cfc7de5f8930a1eed87d88e2703672f8e0f18a9089c25", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "24c8f957049ce616d32ecc6ad0d3b317ce0632e8a6e7521d4fbc6a226a5d73de"}}}, "functions": {}, "sortedMiddleware": ["/"]}