{"kind": "FETCH", "data": {"headers": {"access-control-allow-credentials": "false", "access-control-allow-headers": "Content-Type, Authorization, X-Requested-With", "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS", "access-control-allow-origin": "null", "age": "0", "cache-control": "public, max-age=0, must-revalidate", "content-encoding": "br", "content-type": "application/json", "date": "Sun, 20 Jul 2025 04:49:31 GMT", "server": "Vercel", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "transfer-encoding": "chunked", "x-matched-path": "/api/blog", "x-vercel-cache": "MISS", "x-vercel-id": "bom1::iad1::kdqwc-1752986966965-956c5abe16aa"}, "body": "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", "status": 200, "url": "https://uttam-backend.vercel.app/api/blog"}, "revalidate": 300, "tags": []}