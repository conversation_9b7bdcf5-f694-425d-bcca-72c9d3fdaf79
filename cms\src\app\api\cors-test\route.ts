import { NextRequest, NextResponse } from 'next/server';
import { getAllowedOrigins, isOriginAllowed, setCorsHeaders } from '@/lib/cors';

// Test endpoint to verify CORS configuration
export async function GET(request: NextRequest) {
  const origin = request.headers.get('origin');
  const allowedOrigins = getAllowedOrigins();
  const originAllowed = isOriginAllowed(origin || '', allowedOrigins);

  const response = NextResponse.json({
    success: true,
    cors: {
      requestOrigin: origin,
      allowedOrigins: allowedOrigins,
      originAllowed: originAllowed,
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
    },
    message: originAllowed 
      ? 'CORS is properly configured for this origin' 
      : 'CORS is not configured for this origin'
  });

  setCorsHeaders(response, origin);
  return response;
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('origin');
  const response = new NextResponse(null, { status: 200 });
  setCorsHeaders(response, origin);
  return response;
}
