import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import BlogPost from '@/models/BlogPost';
import { corsHeaders } from '@/lib/cors';

interface RouteParams {
  params: Promise<{ slug: string }>;
}

// GET /api/blog/slug/[slug] - Get blog post by slug
export async function GET(request: NextRequest, { params }: RouteParams) {
  const origin = request.headers.get('origin');
  const headers = corsHeaders(origin || undefined);

  try {
    await connectDB();

    const { slug } = await params;

    const post = await BlogPost.findOne({
      slug: slug,
      status: 'published'
    });

    if (!post) {
      const response = NextResponse.json(
        { success: false, message: 'Blog post not found' },
        { status: 404 }
      );

      Object.entries(headers).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      return response;
    }

    const response = NextResponse.json({
      success: true,
      data: post
    });

    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;

  } catch (error) {
    console.error('Error fetching blog post by slug:', error);
    const response = NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );

    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  }
}

// OPTIONS handler for CORS preflight
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('origin');
  const headers = corsHeaders(origin || undefined);

  return new NextResponse(null, { status: 200, headers });
}
